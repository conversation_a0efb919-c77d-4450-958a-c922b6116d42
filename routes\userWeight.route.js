const express = require('express');
const router = express.Router();
const weightController = require('../controllers/userWeight');


router.post('/Addweight', weightController.addWeight);
router.get('/Getweight/user/:user_id', weightController.getUserWeights);
router.get('/Getweight/:id', weightController.getWeightById);
router.put('/Updateweight/:id', weightController.updateWeight);
router.delete('/Deleteweight/:id', weightController.deleteWeight);

module.exports = router;