const mongoose = require('mongoose');

const waterIntakeSchema = new mongoose.Schema({
    user: { type: mongoose.Schema.Types.ObjectId,
         ref: 'User', required: true },

    about:{
        type: Number,
        default: 11.5,
    },
    watertaken:{
        type: Number,
        required: true,
        default: 0,
    },
    date: { type: Date, default: Date.now },

},{timestamps: true})

module.exports = mongoose.model('WaterIntake', waterIntakeSchema);