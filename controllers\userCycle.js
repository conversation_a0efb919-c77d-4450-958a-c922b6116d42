const UserCycle = require('../models/Cycle.model');
const User = require('../models/Register.model');


const predictNextCycleFromCurrent = (cycleStart, previousCycles) => {
    const currentStart = new Date(cycleStart);

    if (previousCycles.length === 0) {

        return new Date(currentStart.getTime() + (28 * 24 * 60 * 60 * 1000));
    }


    const cycleLengths = [];

    for (let i = 0; i < previousCycles.length - 1; i++) {
        const current = new Date(previousCycles[i].cycleStart);
        const next = new Date(previousCycles[i + 1].cycleStart);
        const daysBetween = Math.abs((current - next) / (1000 * 60 * 60 * 24));
        cycleLengths.push(daysBetween);
    }

    if (cycleLengths.length === 0) {

        return new Date(currentStart.getTime() + (28 * 24 * 60 * 60 * 1000));
    }

    const avgCycleLength = cycleLengths.reduce((a, b) => a + b) / cycleLengths.length;
    return new Date(currentStart.getTime() + (Math.round(avgCycleLength) * 24 * 60 * 60 * 1000));
};

exports.addCycle = async (req, res) => {
    try {
        const { userId, isCycle, cycleStart, cycleEnd } = req.body;

        if (!userId || isCycle === undefined || !cycleStart || !cycleEnd) {
            return res.status(400).json({
                status: 400,
                message: "All fields (userId, isCycle, cycleStart, cycleEnd) are required"
            });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: 404,
                message: "User not found"
            });
        }

        const startDate = new Date(cycleStart);
        const endDate = new Date(cycleEnd);

        if (startDate >= endDate) {
            return res.status(400).json({
                status: 400,
                message: "Cycle start date must be before cycle end date"
            });
        }


        const previousCycles = await UserCycle.find({ userId: userId })
            .sort({ cycleStart: -1 });


        const predictedNextCycle = predictNextCycleFromCurrent(cycleStart, previousCycles);

        const userCycle = await UserCycle.create({
            userId,
            nextCycle: predictedNextCycle,
            isCycle,
            cycleStart: startDate,
            cycleEnd: endDate
        });

        res.status(201).json({
            status: 201,
            message: "Cycle added successfully",
            data: userCycle
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};

exports.getUserCycles = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({
                status: 400,
                message: "User ID is required"
            });
        }

        const cycles = await UserCycle.find({ userId: userId })
            .sort({ cycleStart: -1 });

        res.status(200).json({
            status: 200,
            message: "Cycles retrieved successfully",
            data: cycles,
            count: cycles.length
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};

exports.updateCycle = async (req, res) => {
    try {
        const { id } = req.params;
        const { isCycle, cycleStart, cycleEnd } = req.body;

        const existingCycle = await UserCycle.findById(id);
        if (!existingCycle) {
            return res.status(404).json({
                status: 404,
                message: "Cycle entry not found"
            });
        }

        if (cycleStart && cycleEnd) {
            const startDate = new Date(cycleStart);
            const endDate = new Date(cycleEnd);

            if (startDate >= endDate) {
                return res.status(400).json({
                    status: 400,
                    message: "Cycle start date must be before cycle end date"
                });
            }
        }

        const updateData = {};
        if (isCycle !== undefined) updateData.isCycle = isCycle;
        if (cycleStart) updateData.cycleStart = new Date(cycleStart);
        if (cycleEnd) updateData.cycleEnd = new Date(cycleEnd);

        if (cycleStart) {
            const userId = existingCycle.userId;
            const previousCycles = await UserCycle.find({
                userId: userId,
                _id: { $ne: id }
            }).sort({ cycleStart: -1 });

            updateData.nextCycle = predictNextCycleFromCurrent(cycleStart, previousCycles);
        }

        const updatedCycle = await UserCycle.findByIdAndUpdate(
            id,
            updateData,
            { new: true, runValidators: true }
        );

        res.status(200).json({
            status: 200,
            message: "Cycle updated successfully",
            data: updatedCycle
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};

exports.deleteCycle = async (req, res) => {
    try {
        const { id } = req.params;

        const deletedCycle = await UserCycle.findById(id);
        if (!deletedCycle) {
            return res.status(404).json({
                status: 404,
                message: "Cycle entry not found"
            });
        }

        await UserCycle.findByIdAndDelete(id);

        res.status(200).json({
            status: 200,
            message: "Cycle deleted successfully",
            data: deletedCycle
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};