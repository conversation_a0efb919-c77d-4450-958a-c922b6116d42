const express = require('express');
const waterIntakeController = require('../controllers/userWater');
const router = express.Router();

router.post('/AddWaterIntake', waterIntakeController.addWaterIntake);
router.get('/GetWaterIntake/:userId', waterIntakeController.getUserWaterIntake);
router.put('/UpdateWaterIntake/:id', waterIntakeController.updateWaterIntake);
router.delete('/DeleteWaterIntake/:id', waterIntakeController.deleteWaterIntake);
router.get('/GetTodayWaterIntake/:userId', waterIntakeController.getTodayWaterIntake);


module.exports = router;
