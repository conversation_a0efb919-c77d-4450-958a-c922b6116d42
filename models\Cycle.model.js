const mongoose = require('mongoose');

const cycleSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    nextCycle: {
        type: Date,
        required: true
    },
    isCycle: {
        type: Boolean,
        required: true
    },
    cycleStart: {
        type: Date,
        required: true
    },
    cycleEnd: {
        type: Date,
        required: true
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Cycle', cycleSchema);