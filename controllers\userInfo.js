const UserInfo = require('../models/userInfo.model')
const User = require('../models/Register.model')
exports.UserInfo = async (req,res)=>{
    try {
      const {userId ,dob,cycledates,questions} = req.body


      if (!cycledates || !Array.isArray(cycledates)) {
        return res.status(400).json({
          status: 400,
          message: "Cycle dates must be provided as an array"
        });
      }

      const convertedCycleDates = cycledates.map(dateStr => {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
          throw new Error(`Invalid date format: ${dateStr}`);
        }
        return date;
      });

      const user = await User.findOne({_id:userId})
      if(!user){
        return res.status(400).json({
            status:400,
            message:"User not found"})
      }

      const userInfo = await UserInfo.create({
        userId,
        dob: new Date(dob),
        questions,
        cycledates: convertedCycleDates,
      })

      res.status(200).json({
        status:200,
        message:"User info created",
        userInfo})

    } catch (error) {
        res.status(400).json({
            status:400,
            message:error.message})
    }
}

exports.getUserInfo = async (req,res)=>{
    try {
        const userId = req.body.userId
        const userInfo = await UserInfo.findOne({userId})
        if(!userInfo){
            return res.status(400).json({
                status:400,
                message:"User info not found"})
        }
        res.status(200).json({
            status:200,
            message:"User info found",
            userInfo})
    } catch (error) {
        res.status(400).json({
            status:400,
            message:error.message})
    }
}

exports.updateUserInfo = async (req,res)=>{
    try {
        const userId =req.params.userId
        const userInfo = await UserInfo.findOne({userId})
        if(!userInfo){
            return res.status(400).json({
                status:400,
                message:"User info not found"})
        }
        const {dob,cycledates,questions} = req.body;
        if (cycledates && Array.isArray(cycledates)) {
            userInfo.cycledates = cycledates.map(dateStr => new Date(dateStr));
          }
          if (questions) {
            userInfo.questions = questions;
          }
          if (dob) {
            userInfo.dob = new Date(dob);
          }
          await userInfo.save();
          res.status(200).json({
            status:200,
            message:"User info updated",
            userInfo})
    } catch (error) {
        res.status(400).json({
            status:400,
            message:error.message})
    }
}

exports.deleteUserInfo = async (req,res)=>{
    try {
        const userId = req.params.userId
        const userInfo = await UserInfo.findOne({userId})
        if(!userInfo){
            return res.status(400).json({
                status:400,
                message:"User info not found"})
        }
        await UserInfo.findByIdAndDelete(userId)
        res.status(200).json({
            status:200,
            message:"User info deleted",
            userInfo})
    } catch (error) {
        res.status(400).json({
            status:400,
            message:error.message})
    }
}