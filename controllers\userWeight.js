const UserWeight = require('../models/Weight.model');
const User = require('../models/Register.model');


const calculateAndUpdateAverageWeight = async (userId) => {
    const weights = await UserWeight.find({ user: userId });
    if (weights.length > 0) {
        const totalWeight = weights.reduce((sum, entry) => sum + entry.weight, 0);
        const averageWeight = totalWeight / weights.length;

        await UserWeight.updateMany(
            { user: userId },
            { averageweight: Math.round(averageWeight * 100) / 100 }
        );

        return Math.round(averageWeight * 100) / 100;
    }
    return 0;
};


exports.addWeight = async (req, res) => {
    try {
        const { weight, dateAndtime, user_id } = req.body;
 

        if (!weight || !user_id) {
            return res.status(400).json({
                status: 400,
                message: "Weight and user id are required"
            });
        }

        const user = await User.findById(user_id);
        if (!user) {
            return res.status(404).json({
                status: 404,
                message: "User not found"
            });
        }

         const averageWeight = await calculateAndUpdateAverageWeight(user_id);

        const userWeight = await UserWeight.create({
            weight,
            dateAndtime: dateAndtime || new Date(),
            user: user_id,
            averageweight: averageWeight
        });

        res.status(201).json({
            status: 201,
            message: "Weight added successfully",
            data: userWeight
        });
    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.getUserWeights = async (req, res) => {
    try {
        const { user_id } = req.params;

        if (!user_id) {
            return res.status(400).json({
                status: 400,
                message: "User ID is required"
            });
        }

        const weights = await UserWeight.find({ user: user_id })
            .sort({ dateAndtime: -1 })
            .populate('user', 'name email');

        res.status(200).json({
            status: 200,
            message: "Weights retrieved successfully",
            data: weights,
            count: weights.length
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.getWeightById = async (req, res) => {
    try {
        const { id } = req.params;

        const weight = await UserWeight.findById(id).populate('user', 'name email');

        if (!weight) {
            return res.status(404).json({
                status: 404,
                message: "Weight entry not found"
            });
        }

        res.status(200).json({
            status: 200,
            message: "Weight retrieved successfully",
            data: weight
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.updateWeight = async (req, res) => {
    try {
        const { id } = req.params;
        const { weight, dateAndtime } = req.body;


        const existingWeight = await UserWeight.findById(id);
        if (!existingWeight) {
            return res.status(404).json({
                status: 404,
                message: "Weight entry not found"
            });
        }


        const updatedWeight = await UserWeight.findByIdAndUpdate(
            id,
            {
                ...(weight && { weight }),
                ...(dateAndtime && { dateAndtime })
            },
            { new: true, runValidators: true }
        ).populate('user', 'name email');


        await calculateAndUpdateAverageWeight(existingWeight.user);


        const finalWeight = await UserWeight.findById(id).populate('user', 'name email');

        res.status(200).json({
            status: 200,
            message: "Weight updated successfully",
            data: finalWeight
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.deleteWeight = async (req, res) => {
    try {
        const { id } = req.params;

        const deletedWeight = await UserWeight.findById(id);
        if (!deletedWeight) {
            return res.status(404).json({
                status: 404,
                message: "Weight entry not found"
            });
        }

        const userId = deletedWeight.user;


        await UserWeight.findByIdAndDelete(id);


        await calculateAndUpdateAverageWeight(userId);

        res.status(200).json({
            status: 200,
            message: "Weight deleted successfully",
            data: deletedWeight
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};