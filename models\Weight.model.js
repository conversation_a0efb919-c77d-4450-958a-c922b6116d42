const mongoose = require('mongoose');

const WeightSchema = new mongoose.Schema({
    weight: {
        type: Number,
        required: true
    }
    ,
    dateAndtime: {
        type: Date,
        default: Date.now(),
    },
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    averageweight:{
        type: Number,
        required: false
    }

},
    { timestamps: true })

module.exports = mongoose.model('Weight', WeightSchema);