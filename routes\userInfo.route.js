const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/authMiddleware')
const { UserInfo,getUserInfo,updateUserInfo,deleteUserInfo } = require('../controllers/userInfo');

router.post('/Userinfo', UserInfo);
router.get('/Getuserinfo',getUserInfo);
router.put('/Updateuserinfo/:userId',updateUserInfo);
router.delete('/Deleteuserinfo/:userId',deleteUserInfo);
module.exports = router;