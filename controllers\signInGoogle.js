const { OAuth2Client } = require('google-auth-library');
const User = require('../models/Register.model');
const jwt = require('jsonwebtoken');

exports.signInGoogle = async (req, res) => {
    try{
        const { token } = req.body;
        const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
        const ticket = await client.verifyIdToken({
            idToken: token,
            audience: process.env.GOOGLE_CLIENT_ID
        });
        const { name, email, picture } = ticket.getPayload();
        const user = await User.findOne({ email });
        if (user) {
            return res.status(400).json({ message: 'User already exists' });
        }
        const newUser = new User({
            name,
            email,
            picture
        });
        await newUser.save();
        const jwtToken = jwt.sign({ id: newUser._id }, process.env.JWT_SECRET, {
            expiresIn: '1d'
        });
        res.status(200).json({
            token: jwtToken,
            status: 'success',
            code: 200,
            message: 'User created successfully',
            user: newUser });
    }catch(err){
        res.status(500).json({
            status:500,
            message: err.message});
    }
}