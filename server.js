const express = require('express');
const app = express();
const ConnectDb = require('./config/Db');
const emailRegisterRoute = require('./routes/emailRegister.route');
const userInfoRoute = require('./routes/userInfo.route');
const userWeightRoute = require('./routes/userWeight.route');
const userCycleRoute = require('./routes/userCycle.route');
const waterIntakeRoute = require('./routes/waterIntake.route');

const cors = require('cors');
const port = 3000;

// const signInGoogle = require('./routes/signInGoogle.route');

app.use(cors(
    {
        origin: '*'
    }
));
app.use(express.json());
app.use('/api/v0/registerWithEmail', emailRegisterRoute);
app.use('/api/v0/info', userInfoRoute);
app.use('/api/v0/userWeight', userWeightRoute);
app.use('/api/v0/userCycle', userCycleRoute);
app.use('/api/v0/waterIntake', waterIntakeRoute);

// app.use('/api/v0', signInGoogle);

app.listen(port, async () => {
    await ConnectDb();
    console.log(`http://localhost:${port}`);
});

