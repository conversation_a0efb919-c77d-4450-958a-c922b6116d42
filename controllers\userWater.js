const WaterIntake = require('../models/waterIntake');
const User = require('../models/Register.model');


exports.addWaterIntake = async (req, res) => {
    try {
        const { userId,  watertaken } = req.body;

        if (!userId || !watertaken) {
            return res.status(400).json({
                status: 400,
                message: "userId and waterAmount are required"
            });
        }

        if (watertaken <= 0) {
            return res.status(400).json({
                status: 400,
                message: "Water amount must be greater than 0"
            });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                status: 404,
                message: "User not found"
            });
        }


        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);


        let waterIntake = await WaterIntake.findOne({
            user: userId,
            date: {
                $gte: startOfDay,
                $lt: endOfDay
            }
        });

        if (waterIntake) {

            waterIntake.waterTaken += watertaken;
            await waterIntake.save();

            return res.status(200).json({
                status: 200,
                message: "Water intake updated successfully",
                data: waterIntake
            });
        } else {

            waterIntake = await WaterIntake.create({
                user: userId,
                waterTaken: watertaken,
                date: today
            });

            return res.status(201).json({
                status: 201,
                message: "Water intake added successfully",
                data: waterIntake
            });
        }

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.getUserWaterIntake = async (req, res) => {
    try {
        const { userId } = req.params;
        const { date, limit = 30 } = req.query;

        if (!userId) {
            return res.status(400).json({
                status: 400,
                message: "User ID is required"
            });
        }

        let query = { user: userId };


        if (date) {
            const queryDate = new Date(date);
            const startOfDay = new Date(queryDate.getFullYear(), queryDate.getMonth(), queryDate.getDate());
            const endOfDay = new Date(queryDate.getFullYear(), queryDate.getMonth(), queryDate.getDate() + 1);

            query.date = {
                $gte: startOfDay,
                $lt: endOfDay
            };
        }

        const waterIntakes = await WaterIntake.find(query)
            .populate('user', 'name email')
            .sort({ date: -1 })
            .limit(parseInt(limit));

        res.status(200).json({
            status: 200,
            message: "Water intake records retrieved successfully",
            data: waterIntakes,
            count: waterIntakes.length
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.getTodayWaterIntake = async (req, res) => {
    try {
        const { userId } = req.params;

        if (!userId) {
            return res.status(400).json({
                status: 400,
                message: "User ID is required"
            });
        }

        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

        const waterIntake = await WaterIntake.findOne({
            user: userId,
            date: {
                $gte: startOfDay,
                $lt: endOfDay
            }
        }).populate('user', 'name email');

        if (!waterIntake) {
            return res.status(200).json({
                status: 200,
                message: "No water intake record for today",
                data: {
                    user: userId,
                    about: 11.5,
                    waterTaken: 0,
                    percentage: 0
                }
            });
        }


        const percentage = Math.round((waterIntake.waterTaken / waterIntake.about) * 100);

        res.status(200).json({
            status: 200,
            message: "Today's water intake retrieved successfully",
            data: {
                ...waterIntake.toObject(),
                percentage: percentage
            }
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.updateWaterIntake = async (req, res) => {
    try {
        const { id } = req.params;
        const { waterTaken } = req.body;

        if (!waterTaken || waterTaken < 0) {
            return res.status(400).json({
                status: 400,
                message: "Valid waterTaken amount is required"
            });
        }

        const waterIntake = await WaterIntake.findById(id);
        if (!waterIntake) {
            return res.status(404).json({
                status: 404,
                message: "Water intake record not found"
            });
        }

        waterIntake.waterTaken = waterTaken;
        await waterIntake.save();

        res.status(200).json({
            status: 200,
            message: "Water intake updated successfully",
            data: waterIntake
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};


exports.deleteWaterIntake = async (req, res) => {
    try {
        const { id } = req.params;

        const waterIntake = await WaterIntake.findById(id);
        if (!waterIntake) {
            return res.status(404).json({
                status: 404,
                message: "Water intake record not found"
            });
        }

        await WaterIntake.findByIdAndDelete(id);

        res.status(200).json({
            status: 200,
            message: "Water intake record deleted successfully",
            data: waterIntake
        });

    } catch (e) {
        res.status(500).json({
            status: 500,
            message: e.message
        });
    }
};