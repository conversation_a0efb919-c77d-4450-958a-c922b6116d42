const User = require('../models/Register.model');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

exports.registerEmail = async (req, res) => {
    try{
        const { fullname, email, password ,confirmPassword } = req.body;
        const  profile  = req.file;

        if(!fullname || !email || !password || !confirmPassword) {
            return res.status(400).json({ message: 'Please fill all fields' });
        }

        if(!email.includes('@')){
            return res.status(400).json({ message: 'Please enter a valid email' });
        }

        if (password.length < 6 || confirmPassword.length < 6) {

            return res.status(400).json({ message: 'Password must be at least 6 characters' });
        }


        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/;
        if (!passwordRegex.test(password)) {
            return res.status(400).json({
                message: 'Password must contain at least 1 uppercase letter, 1 lowercase letter, and 1 number'
            });
        }
        if (password !== confirmPassword) {
            return res.status(400).json({ message: 'Passwords do not match' });
        }
        const user = await User.findOne({ email });
        if (user) {
            return res.status(400).json({ message: 'User already exists' });
        }

            const imageBase64 = profile.buffer.toString('base64');
           const mimeType = profile.mimetype;
            const profileImage = `data:${mimeType};base64,${imageBase64}`;


        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);
        const hashedConfirmPassword = await bcrypt.hash(confirmPassword, salt);
        const newUser = new User({
            fullname,
            email,
            password:hashedPassword,
            confirmPassword:hashedConfirmPassword,
            profile:profileImage
        });
        await newUser.save();
        const jwtToken = jwt.sign({ id: newUser._id, name: newUser.name, email: newUser.email }, process.env.JWT_SECRET, {
            expiresIn: '1d'
        });
        res.status(200).json({
            token: jwtToken,
            status: 'success',
            code: 200,
            message: 'User registered successfully',
            user: newUser });
    }catch(err){
        res.status(500).json({
            status:500,
            message: err.message});
    }
}
exports.loginEmail = async (req, res) => {
    try{
        const { email, password } = req.body;
        const user = await User.findOne({ email });
        if (!user) {
            return res.status(400).json({ message: 'User does not exist' });
        }
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.status(400).json({ message: 'Invalid credentials' });
        }
        const jwtToken = jwt.sign({ id: user._id, name: user.name, email: user.email }, process.env.JWT_SECRET, {
            expiresIn: '1d'
        });
        res.status(200).json({
            token: jwtToken,
            status: 'success',
            code: 200,
            message: 'User logged in successfully',
            user: user });
    }catch(err){
        res.status(500).json({
            status:500,
            message: err.message});
    }
}
